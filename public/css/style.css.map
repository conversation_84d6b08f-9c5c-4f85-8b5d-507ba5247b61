{"version": 3, "file": "css/style.css", "mappings": "AACA;EAAI;ACCJ;;ADAA;EACI;EACA;EACA;ACGJ;;ADDA;EACI;ACIJ;;ACZA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;ADaJ;ACVI;EACI;ADYR;ACRI;EACI;ADUR;;ACNA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;ADOJ;ACJI;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADMR;;ACDA;EACI;IAEI;IACA;IAGA;IACA;IAGA;IACA;EDCN;ECEM;IACI;IACA;IACA;EDAV;AACF;ACKA;EACI;EACA;EACA;EACA;EACA;ADHJ;ACKI;EACI;ADHR;;ACOA;EAEI;EACA;EACA;ADLJ;ACQI;EACI;ADNR;;ACUA;EACI;EACA;EACA;KAAA;ADPJ;;ACUA;EAEI;EACA;EACA;EACA;ADRJ;;ACWA;EAEI;ADTJ;;ACYA;EACI;ADTJ;;ACaA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADVJ;ACYI;;EACI;ADTR;ACYI;;EACI;EACA;ADTR;;ACgBI;;EACI;EACA;ADZR;ACiBQ;;EACI;EACA;ADdZ;ACkBQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADfZ;ACoBI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADjBR;;ACsBA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADnBJ;ACsBI;EAjBJ;IAkBQ;IACA;EDnBN;AACF;ACsBI;EAEI;EACA;EACA;ADrBR;;AC0BA;EACI;EACA;EACA;EACA;ADvBJ;;AC2BA;EACI;ADxBJ;;AC2BA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADxBJ;AC0BI;;EACI;ADvBR;AC0BI;;EACI;EACA;EACA;ADvBR;;AC2BA;EACI;EACA;EACA;EACA;EACA;ADxBJ;;AC4BA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADzBJ;AC2BI;EACI;ADzBR;AC4BI;EACI;EACA;AD1BR;AC8BI;EACI;AD5BR;;ACgCA;EACI;EACA;EACA;EACA;EACA;EACA;AD7BJ;;ACgCA;EACI;EACA;EACA;EACA;EACA;AD7BJ;;ACgCA;EACI;EACA;EACA;AD7BJ;;ACmCQ;EACI;ADhCZ;ACkCQ;EACI;ADhCZ;ACkCQ;EACI;ADhCZ;;ACsCA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;ADnCJ;ACsCI;EACI;EACA;EACA;ADpCR;;ACwCA;EACI;EACA;ADrCJ;;ACyCA;EACI;EACA;EACA;EACA;EACA;ADtCJ;ACyCI;EAAiB;ADtCrB;ACuCI;EAAiB;ADpCrB;ACqCI;EAAiB;ADlCrB;ACmCI;EAAiB;ADhCrB;ACiCI;EAAiB;AD9BrB;AC+BI;EAAiB;AD5BrB;;AC+BA;EACI;IACI;IACA;ED5BN;AACF;ACgCA;;EAEI;EACA;EACA;AD9BJ;;ACiCA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;AD9BJ;ACgCI;;EACI;AD7BR;ACgCI;;EACI;EACA;AD7BR;;ACkCA;;EAEI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD/BJ;ACiCI;;EACI;AD9BR;ACiCI;;EACI;EACA;AD9BR;;AC+CI;;EACI;EACA;AD3CR;ACgDQ;;EACI;EACA;AD7CZ;ACiDQ;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AD9CZ;ACmDI;;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADhDR;;ACsDA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADnDJ;ACqDI;EACI;ADnDR;ACsDI;EACI;EACA;ADpDR;;ACwDA;EACI;EACA;ADrDJ;;ACwDA;;EAEI;EACA;EACA;ADrDJ;;ACwDA;EACI;EACA;EACA;ADrDJ;;ACyDA;;EAEI;EACA;EACA;ADtDJ;ACwDI;;EACI;ADrDR;;ACyDA;EACI;ADtDJ;;AC0DA;EACI;EACA;EACA;EACA;EACA;ADvDJ;;AC0DA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ADvDJ;ACyDI;EACI;ADvDR;AC0DI;EACI;EACA;ADxDR;;AC6DA;EACI;IACI;ED1DN;AACF;AC8DA;EACI;IACI;ED5DN;AACF;ACgEA;EACI;IACI;ED9DN;ECiEE;IACI;ED/DN;ECmEE;IACI;EDjEN;ECmEM;IACI;EDjEV;ECqEM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EDnEV;ECyEM;IACI;IACA;IACA;IACA;IACA;IACA;IACA;EDvEV;AACF;AC2EA;EACI;IACI;EDzEN;EC4EE;IACI;ED1EN;AACF;AC8EA;EAGQ;IACI;IACA;IACA;ED9EV;ECkFM;IACI;IACA;IACA;EDhFV;AACF;ACqFA;EAEI;EACA;EACA;ADpFJ;ACsFI;EACI;ADpFR;;ACyFA;;;EAGI;EACA;EACA;ADtFJ,C", "sources": ["webpack:///./resources/css/partials/_basics.scss", "webpack:///./resources/css/style.scss", "webpack:///./resources/css/partials/_nav.scss"], "sourcesContent": ["// Basic reset\n* { box-sizing: border-box; }\nhtml, body { \n    margin: 0; \n    padding: 0;\n    font-family: 'Inter', sans-serif;\n}\nmain {\n    overflow-x: hidden;\n}\n\n", "* {\n  box-sizing: border-box;\n}\n\nhtml, body {\n  margin: 0;\n  padding: 0;\n  font-family: \"Inter\", sans-serif;\n}\n\nmain {\n  overflow-x: hidden;\n}\n\n.navbar-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  padding: 0 80px;\n  position: fixed;\n  top: 20px;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  transform: translateY(0);\n  transition: transform 0.4s ease-in-out;\n}\n.navbar-container.nav-hidden {\n  transform: translateY(-200%);\n}\n.navbar-container.nav-visible {\n  transform: translateY(0);\n}\n\nnav {\n  position: relative;\n  width: 100%;\n  max-width: 1440px;\n  display: flex;\n  align-items: center;\n  padding: 16px;\n  border-radius: 10rem;\n  color: white;\n  transition: all 0.4s ease;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\nnav.nav-mobile-expanded {\n  border-radius: 1.5rem;\n  flex-direction: column;\n  align-items: stretch;\n  min-height: 100vh;\n  max-width: none;\n  width: 100%;\n  margin: 0;\n  padding: 16px;\n}\n\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n  nav {\n    background: rgba(0, 0, 0, 0.4);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    -webkit-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    -moz-box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n  }\n  nav.nav-mobile-expanded {\n    background: rgba(0, 0, 0, 0.6);\n    -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n    backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n  }\n}\n.nav-mobile-header {\n  display: none;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  margin-bottom: 2rem;\n}\n.nav-mobile-expanded .nav-mobile-header {\n  display: flex;\n}\n\n.nav-logo {\n  flex: 0 0 auto;\n  max-width: 40px;\n  max-height: 40px;\n}\n.nav-mobile-expanded .nav-logo {\n  display: none;\n}\n\n.nav-logo img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.nav-collection {\n  display: flex;\n  align-items: center;\n  flex: 1 1 auto;\n  justify-content: center;\n}\n\n.nav-cta {\n  flex: 0 0 auto;\n}\n\n.nav-item {\n  position: relative;\n}\n\n.nav-link,\n.nav-toggle {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  color: inherit;\n  padding: 0.5rem 1rem;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n}\n.nav-link:hover,\n.nav-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-link:focus,\n.nav-toggle:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-toggle .nav-arrow,\n.nav-toggle-level2 .nav-arrow {\n  transition: all 0.3s ease;\n  opacity: 1;\n}\n.nav-toggle[aria-expanded=true] .nav-arrow,\n.nav-toggle-level2[aria-expanded=true] .nav-arrow {\n  opacity: 0;\n  transform: scale(0.8);\n}\n.nav-toggle[aria-expanded=true]::after,\n.nav-toggle-level2[aria-expanded=true]::after {\n  content: \"\";\n  position: absolute;\n  width: 10px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 1;\n  transform: scale(1);\n  transition: all 0.3s ease 0.1s;\n  right: 0.5rem;\n}\n.nav-toggle::after,\n.nav-toggle-level2::after {\n  content: \"\";\n  position: absolute;\n  width: 10px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n  right: 0.5rem;\n}\n\n.nav-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  min-width: 200px;\n  background: rgba(0, 0, 0, 0.9);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 0.5rem;\n  padding: 0.5rem 0;\n  margin-top: 0.5rem;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px);\n  transition: all 0.2s ease;\n  z-index: 1000;\n}\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n  .nav-dropdown {\n    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n  }\n}\n.nav-item:hover .nav-dropdown, .nav-item.nav-active .nav-dropdown {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0);\n}\n\n.nav-dropdown-level2 {\n  top: 0;\n  left: 100%;\n  margin-top: 0;\n  margin-left: 0.5rem;\n}\n\n.nav-dropdown-item {\n  position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 0.75rem 1rem;\n  text-decoration: none;\n  color: inherit;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s ease;\n}\n.nav-dropdown-link:hover,\n.nav-toggle-level2:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-dropdown-link:focus,\n.nav-toggle-level2:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: -2px;\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.nav-cta a {\n  text-decoration: none;\n  color: inherit;\n  padding: 16px 24px;\n  border-radius: 10rem;\n  background-color: black;\n}\n\n.nav-mobile-toggle {\n  display: none;\n  align-items: center;\n  gap: 0.5rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  padding: 0.5rem;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n  margin-left: auto;\n}\n.nav-mobile-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-mobile-toggle:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n.nav-mobile-expanded .nav-mobile-toggle {\n  display: none;\n}\n\n.hamburger-icon {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  width: 18px;\n  height: 14px;\n  order: 2;\n}\n\n.hamburger-line {\n  width: 100%;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n  font-size: 0.9rem;\n  font-weight: 500;\n  order: 1;\n}\n\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(1) {\n  transform: rotate(45deg) translate(5px, 5px);\n}\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(2) {\n  opacity: 0;\n}\n.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(3) {\n  transform: rotate(-45deg) translate(7px, -6px);\n}\n\n.nav-mobile-content {\n  display: none;\n  flex-direction: column;\n  flex: 1;\n  width: 100%;\n  opacity: 0;\n  transform: translateY(20px);\n  transition: all 0.4s ease 0.2s;\n}\n.nav-mobile-expanded .nav-mobile-content {\n  display: flex;\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.nav-mobile-items {\n  flex: 1;\n  overflow-y: auto;\n}\n\n.nav-mobile-item {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 0 1rem;\n  opacity: 0;\n  transform: translateY(10px);\n  animation: fadeInUp 0.4s ease forwards;\n}\n.nav-mobile-item:nth-child(1) {\n  animation-delay: 0.3s;\n}\n.nav-mobile-item:nth-child(2) {\n  animation-delay: 0.4s;\n}\n.nav-mobile-item:nth-child(3) {\n  animation-delay: 0.5s;\n}\n.nav-mobile-item:nth-child(4) {\n  animation-delay: 0.6s;\n}\n.nav-mobile-item:nth-child(5) {\n  animation-delay: 0.7s;\n}\n.nav-mobile-item:nth-child(6) {\n  animation-delay: 0.8s;\n}\n\n@keyframes fadeInUp {\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n  display: flex;\n  align-items: center;\n  width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n  flex: 1;\n  padding: 1rem 0;\n  text-decoration: none;\n  color: inherit;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.nav-mobile-item-header .nav-mobile-link:hover,\n.nav-mobile-subitem-header .nav-mobile-link:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.nav-mobile-item-header .nav-mobile-link:focus,\n.nav-mobile-subitem-header .nav-mobile-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 48px;\n  height: 48px;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s ease;\n}\n.nav-mobile-toggle-item:hover,\n.nav-mobile-toggle-subitem:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n.nav-mobile-toggle-item:focus,\n.nav-mobile-toggle-subitem:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-toggle-item .nav-arrow,\n.nav-mobile-toggle-subitem .nav-arrow {\n  transition: all 0.3s ease;\n  opacity: 1;\n}\n.nav-mobile-toggle-item[aria-expanded=true] .nav-arrow,\n.nav-mobile-toggle-subitem[aria-expanded=true] .nav-arrow {\n  opacity: 0;\n  transform: scale(0.8);\n}\n.nav-mobile-toggle-item[aria-expanded=true]::after,\n.nav-mobile-toggle-subitem[aria-expanded=true]::after {\n  content: \"\";\n  position: absolute;\n  width: 12px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 1;\n  transform: scale(1);\n  transition: all 0.3s ease 0.1s;\n}\n.nav-mobile-toggle-item::after,\n.nav-mobile-toggle-subitem::after {\n  content: \"\";\n  position: absolute;\n  width: 12px;\n  height: 2px;\n  background-color: currentColor;\n  border-radius: 1px;\n  opacity: 0;\n  transform: scale(0.8);\n  transition: all 0.3s ease;\n}\n\n.nav-mobile-link {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 1rem 0;\n  text-decoration: none;\n  color: inherit;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.nav-mobile-link:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.nav-mobile-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n.nav-mobile-link-level1 {\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n  padding-left: 1rem;\n  font-size: 1rem;\n  font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n  padding-left: 2rem;\n  font-size: 0.9rem;\n  font-weight: 400;\n}\n\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n  max-height: 0;\n  overflow: hidden;\n  transition: max-height 0.3s ease;\n}\n.nav-mobile-submenu.nav-mobile-submenu-active,\n.nav-mobile-subsubmenu.nav-mobile-submenu-active {\n  max-height: 500px;\n}\n\n.nav-mobile-subitem {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.nav-mobile-cta {\n  margin-top: auto;\n  padding: 2rem 1rem 1rem;\n  opacity: 0;\n  transform: translateY(20px);\n  animation: fadeInUp 0.4s ease 0.9s forwards;\n}\n\n.nav-mobile-cta-button {\n  display: block;\n  width: 100%;\n  padding: 16px 24px;\n  text-align: center;\n  text-decoration: none;\n  color: white;\n  background-color: black;\n  border-radius: 10rem;\n  font-size: 1rem;\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n}\n.nav-mobile-cta-button:hover {\n  background-color: rgba(0, 0, 0, 0.8);\n}\n.nav-mobile-cta-button:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.5);\n  outline-offset: 2px;\n}\n\n@media (max-width: 1600px) {\n  .navbar-container {\n    padding: 0 20px;\n  }\n}\n@media (max-width: 1200px) {\n  .nav-cta {\n    display: none;\n  }\n}\n@media (max-width: 1024px) {\n  .nav-desktop {\n    display: none;\n  }\n  .nav-mobile-toggle {\n    display: flex;\n  }\n  nav {\n    justify-content: space-between;\n  }\n  nav .nav-logo {\n    flex: 0 0 auto;\n  }\n  nav.nav-mobile-expanded {\n    position: fixed;\n    top: 20px;\n    left: 20px;\n    right: 20px;\n    bottom: 20px;\n    max-width: none;\n    width: auto;\n    height: auto;\n    z-index: 1000;\n  }\n  .navbar-container.nav-expanded {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    padding: 20px;\n    z-index: 1000;\n  }\n}\n@media (max-width: 768px) {\n  .navbar-container {\n    padding: 0 20px;\n  }\n  nav {\n    padding: 12px;\n  }\n}\n@media (min-width: 769px) {\n  .nav-item:hover .nav-dropdown {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n  }\n  .nav-item .nav-dropdown-item:hover .nav-dropdown-level2 {\n    opacity: 1;\n    visibility: visible;\n    transform: translateY(0);\n  }\n}\n.nav-dropdown {\n  animation-duration: 0.2s;\n  animation-timing-function: ease-out;\n  animation-fill-mode: both;\n}\n.nav-dropdown.nav-dropdown-level2 {\n  animation-delay: 0.1s;\n}\n\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n  outline: 2px solid rgba(255, 255, 255, 0.8);\n  outline-offset: 2px;\n  background-color: rgba(255, 255, 255, 0.15);\n}", ".navbar-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 100%;\n    padding: 0 80px; // 80px padding on sides\n    position: fixed;\n    top: 20px;\n    left: 0;\n    right: 0;\n    z-index: 100;\n\n    // Smooth transitions for show/hide\n    transform: translateY(0);\n    transition: transform 0.4s ease-in-out;\n\n    // Hidden state (when scrolling down)\n    &.nav-hidden {\n        transform: translateY(-200%);\n    }\n\n    // Visible state (when scrolling up or at top)\n    &.nav-visible {\n        transform: translateY(0);\n    }\n}\n\nnav {\n    position: relative;\n    width: 100%; // Full width within container\n    max-width: 1440px;\n    display: flex;\n    align-items: center;\n    padding: 16px; // Internal padding for nav content\n    border-radius: 10rem;\n    color: white;\n    transition: all 0.4s ease;\n\n    // Fallback for unsupported browsers\n    background: rgba(255, 255, 255, 0.95);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    // Mobile expanded state\n    &.nav-mobile-expanded {\n        border-radius: 1.5rem;\n        flex-direction: column;\n        align-items: stretch;\n        min-height: 100vh;\n        max-width: none;\n        width: 100%;\n        margin: 0;\n        padding: 16px;\n    }\n}\n\n// Enhanced frosted glass effect for supported browsers\n@supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n    nav {\n        // Semi-transparent background with brand color tint (#016449)\n        background: rgba(0, 0, 0, 0.4);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        -webkit-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        -moz-box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow:inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1);\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n        // Apply backdrop filter directly to nav\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n\n        // Enhanced backdrop filter for expanded mobile state\n        &.nav-mobile-expanded {\n            background: rgba(0, 0, 0, 0.6);\n            -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n            backdrop-filter: blur(20px) saturate(150%) brightness(80%);\n        }\n    }\n}\n// Nav content positioning with proper flexbox layout\n// Mobile header layout (logo + toggle in expanded state)\n.nav-mobile-header {\n    display: none;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%;\n    margin-bottom: 2rem;\n\n    .nav-mobile-expanded & {\n        display: flex;\n    }\n}\n\n.nav-logo {\n    // Logo at flex-start (default)\n    flex: 0 0 auto;\n    max-width: 40px;\n    max-height: 40px;\n\n    // Hide regular logo when mobile is expanded (use mobile header logo instead)\n    .nav-mobile-expanded & {\n        display: none;\n    }\n}\n\n.nav-logo img {\n    width: 100%;\n    height: 100%;\n    object-fit: contain;\n}\n\n.nav-collection {\n    // Navigation items in center\n    display: flex;\n    align-items: center;\n    flex: 1 1 auto;\n    justify-content: center;\n}\n\n.nav-cta {\n    // CTA at flex-end\n    flex: 0 0 auto;\n}\n\n.nav-item {\n    position: relative;\n}\n\n// Base navigation links and buttons\n.nav-link,\n.nav-toggle {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 1rem;\n    font-weight: 500;\n    text-decoration: none;\n    color: inherit;\n    padding: 0.5rem 1rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Arrow animation for dropdowns (chevron to minus)\n.nav-toggle,\n.nav-toggle-level2 {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 10px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s;\n            right: 0.5rem; // Position for desktop dropdowns\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 10px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n        right: 0.5rem; // Position for desktop dropdowns\n    }\n}\n\n// Dropdown containers\n.nav-dropdown {\n    position: absolute;\n    top: 100%;\n    left: 0;\n    min-width: 200px;\n    background: rgba(0, 0, 0, 0.9);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n    border-radius: 0.5rem;\n    padding: 0.5rem 0;\n    margin-top: 0.5rem;\n    opacity: 0;\n    visibility: hidden;\n    transform: translateY(-10px);\n    transition: all 0.2s ease;\n    z-index: 1000;\n\n    // Backdrop filter for supported browsers\n    @supports (backdrop-filter: blur(16px)) or (-webkit-backdrop-filter: blur(16px)) {\n        -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n        backdrop-filter: blur(16px) saturate(140%) brightness(90%);\n    }\n\n    // Show dropdown when parent is active\n    .nav-item:hover &,\n    .nav-item.nav-active & {\n        opacity: 1;\n        visibility: visible;\n        transform: translateY(0);\n    }\n}\n\n// Level 2 dropdown positioning\n.nav-dropdown-level2 {\n    top: 0;\n    left: 100%;\n    margin-top: 0;\n    margin-left: 0.5rem;\n}\n\n// Dropdown items\n.nav-dropdown-item {\n    position: relative;\n}\n\n.nav-dropdown-link,\n.nav-toggle-level2 {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    padding: 0.75rem 1rem;\n    text-decoration: none;\n    color: inherit;\n    background: none;\n    border: none;\n    cursor: pointer;\n    font-size: 0.9rem;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: -2px;\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n}\n\n.nav-cta a {\n    text-decoration: none;\n    color: inherit;\n    padding: 16px 24px;\n    border-radius: 10rem;\n    background-color: black;\n}\n\n// Mobile Menu Toggle Button\n.nav-mobile-toggle {\n    display: none;\n    align-items: center;\n    gap: 0.5rem;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    padding: 0.5rem;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n    margin-left: auto; // Push to the right side\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n\n    // Hide regular toggle when mobile is expanded (use mobile header toggle instead)\n    .nav-mobile-expanded & {\n        display: none;\n    }\n}\n\n.hamburger-icon {\n    display: flex;\n    flex-direction: column;\n    gap: 3px;\n    width: 18px;\n    height: 14px;\n    order: 2; // Place icon after text\n}\n\n.hamburger-line {\n    width: 100%;\n    height: 2px;\n    background-color: currentColor;\n    border-radius: 1px;\n    transition: all 0.3s ease;\n}\n\n.nav-mobile-toggle-text {\n    font-size: 0.9rem;\n    font-weight: 500;\n    order: 1; // Place text before icon\n}\n\n// Hamburger animation when menu is open\n.nav-mobile-toggle[aria-expanded=\"true\"] {\n    .hamburger-line {\n        &:nth-child(1) {\n            transform: rotate(45deg) translate(5px, 5px);\n        }\n        &:nth-child(2) {\n            opacity: 0;\n        }\n        &:nth-child(3) {\n            transform: rotate(-45deg) translate(7px, -6px);\n        }\n    }\n}\n\n// Mobile Menu Content (inside expanded nav)\n.nav-mobile-content {\n    display: none;\n    flex-direction: column;\n    flex: 1;\n    width: 100%;\n    opacity: 0;\n    transform: translateY(20px);\n    transition: all 0.4s ease 0.2s; // Delay for smooth entrance\n\n    // Show when nav is expanded\n    .nav-mobile-expanded & {\n        display: flex;\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.nav-mobile-items {\n    flex: 1;\n    overflow-y: auto;\n}\n\n// Mobile Menu Items\n.nav-mobile-item {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    padding: 0 1rem;\n    opacity: 0;\n    transform: translateY(10px);\n    animation: fadeInUp 0.4s ease forwards;\n\n    // Stagger animation for each item\n    &:nth-child(1) { animation-delay: 0.3s; }\n    &:nth-child(2) { animation-delay: 0.4s; }\n    &:nth-child(3) { animation-delay: 0.5s; }\n    &:nth-child(4) { animation-delay: 0.6s; }\n    &:nth-child(5) { animation-delay: 0.7s; }\n    &:nth-child(6) { animation-delay: 0.8s; }\n}\n\n@keyframes fadeInUp {\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n// Split header for items with submenus\n.nav-mobile-item-header,\n.nav-mobile-subitem-header {\n    display: flex;\n    align-items: center;\n    width: 100%;\n}\n\n.nav-mobile-item-header .nav-mobile-link,\n.nav-mobile-subitem-header .nav-mobile-link {\n    flex: 1;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n// Toggle buttons for submenus\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 48px;\n    height: 48px;\n    background: none;\n    border: none;\n    cursor: pointer;\n    color: inherit;\n    border-radius: 0.5rem;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n\n    // // Animate the chevron icon\n    // .nav-arrow {\n    //     transition: transform 0.3s ease;\n    // }\n\n    // // Rotate chevron when expanded\n    // &[aria-expanded=\"true\"] .nav-arrow {\n    //     transform: rotate(180deg);\n    // }\n}\n\n// Icon swap animation: chevron to minus (active)\n.nav-mobile-toggle-item,\n.nav-mobile-toggle-subitem {\n    .nav-arrow {\n        transition: all 0.3s ease;\n        opacity: 1;\n    }\n\n    // Hide chevron and show minus when expanded\n    &[aria-expanded=\"true\"] {\n        .nav-arrow {\n            opacity: 0;\n            transform: scale(0.8);\n        }\n\n        // Add minus icon after chevron fades out\n        &::after {\n            content: '';\n            position: absolute;\n            width: 12px;\n            height: 2px;\n            background-color: currentColor;\n            border-radius: 1px;\n            opacity: 1;\n            transform: scale(1);\n            transition: all 0.3s ease 0.1s; // Slight delay for smooth transition\n        }\n    }\n\n    // Initially hide the minus icon\n    &::after {\n        content: '';\n        position: absolute;\n        width: 12px;\n        height: 2px;\n        background-color: currentColor;\n        border-radius: 1px;\n        opacity: 0;\n        transform: scale(0.8);\n        transition: all 0.3s ease;\n    }\n}\n\n\n// Regular mobile links (no submenus)\n.nav-mobile-link {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    padding: 1rem 0;\n    text-decoration: none;\n    color: inherit;\n    font-size: 1.1rem;\n    font-weight: 500;\n    transition: color 0.2s ease;\n\n    &:hover {\n        color: rgba(255, 255, 255, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n.nav-mobile-link-level1 {\n    font-size: 1.2rem;\n    font-weight: 600;\n}\n\n.nav-mobile-link-level2,\n.nav-mobile-subitem-header .nav-mobile-link {\n    padding-left: 1rem;\n    font-size: 1rem;\n    font-weight: 400;\n}\n\n.nav-mobile-link-level3 {\n    padding-left: 2rem;\n    font-size: 0.9rem;\n    font-weight: 400;\n}\n\n// Mobile Submenus\n.nav-mobile-submenu,\n.nav-mobile-subsubmenu {\n    max-height: 0;\n    overflow: hidden;\n    transition: max-height 0.3s ease;\n\n    &.nav-mobile-submenu-active {\n        max-height: 500px; // Adjust based on content\n    }\n}\n\n.nav-mobile-subitem {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n// Mobile CTA at bottom\n.nav-mobile-cta {\n    margin-top: auto;\n    padding: 2rem 1rem 1rem;\n    opacity: 0;\n    transform: translateY(20px);\n    animation: fadeInUp 0.4s ease 0.9s forwards; // Animate in last\n}\n\n.nav-mobile-cta-button {\n    display: block;\n    width: 100%;\n    padding: 16px 24px;\n    text-align: center;\n    text-decoration: none;\n    color: white;\n    background-color: black; // Keep original styling\n    border-radius: 10rem; // Keep original rounded styling\n    font-size: 1rem;\n    font-weight: 500;\n    transition: background-color 0.2s ease;\n\n    &:hover {\n        background-color: rgba(0, 0, 0, 0.8);\n    }\n\n    &:focus {\n        outline: 2px solid rgba(255, 255, 255, 0.5);\n        outline-offset: 2px;\n    }\n}\n\n//Decrease padding after 1600px\n@media (max-width: 1600px) {\n    .navbar-container {\n        padding: 0 20px;\n    }\n}\n\n//Remove CTA after 1200px\n@media (max-width: 1200px) {\n    .nav-cta {\n        display: none;\n    }\n}\n\n// Mobile navigation breakpoint\n@media (max-width: 1024px) {\n    .nav-desktop {\n        display: none;\n    }\n\n    .nav-mobile-toggle {\n        display: flex;\n    }\n\n    // Ensure proper flex layout in mobile mode\n    nav {\n        justify-content: space-between;\n\n        .nav-logo {\n            flex: 0 0 auto;\n        }\n\n        // When expanded, take full viewport\n        &.nav-mobile-expanded {\n            position: fixed;\n            top: 20px;\n            left: 20px;\n            right: 20px;\n            bottom: 20px;\n            max-width: none;\n            width: auto;\n            height: auto;\n            z-index: 1000;\n        }\n    }\n\n    // Adjust container for expanded state\n    .navbar-container {\n        &.nav-expanded {\n            position: fixed;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            padding: 20px;\n            z-index: 1000;\n        }\n    }\n}\n\n@media (max-width: 768px) {\n    .navbar-container {\n        padding: 0 20px; // Reduced padding on mobile\n    }\n\n    nav {\n        padding: 12px; // Reduced nav padding on mobile\n    }\n}\n\n// Enhanced hover effects for desktop\n@media (min-width: 769px) {\n    .nav-item {\n        // Hover to show dropdown\n        &:hover .nav-dropdown {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n\n        // Nested hover for level 2\n        .nav-dropdown-item:hover .nav-dropdown-level2 {\n            opacity: 1;\n            visibility: visible;\n            transform: translateY(0);\n        }\n    }\n}\n\n// Animation improvements\n.nav-dropdown {\n    // Smooth entrance animation\n    animation-duration: 0.2s;\n    animation-timing-function: ease-out;\n    animation-fill-mode: both;\n\n    &.nav-dropdown-level2 {\n        animation-delay: 0.1s; // Slight delay for nested dropdowns\n    }\n}\n\n// Focus management for accessibility\n.nav-toggle:focus,\n.nav-toggle-level2:focus,\n.nav-dropdown-link:focus {\n    outline: 2px solid rgba(255, 255, 255, 0.8);\n    outline-offset: 2px;\n    background-color: rgba(255, 255, 255, 0.15);\n}"], "names": [], "sourceRoot": ""}