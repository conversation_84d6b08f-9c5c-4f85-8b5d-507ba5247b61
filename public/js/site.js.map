{"version": 3, "file": "/js/site.js", "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA;AACO,SAASA,oBAAoB,GAAG;EACrC,IAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;EAC1D,IAAI,CAACF,MAAM,EAAE;EAEb,IAAIG,WAAW,GAAGC,MAAM,CAACC,OAAO;;EAEhC;EACA,SAASC,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAE;IAC7B,IAAIC,UAAU;IACd,OAAO,YAAW;MAChB,IAAMC,IAAI,GAAGC,SAAS;MACtB,IAAMC,OAAO,GAAG,IAAI;MACpB,IAAI,CAACH,UAAU,EAAE;QACfF,IAAI,CAACM,KAAK,CAACD,OAAO,EAAEF,IAAI,CAAC;QACzBD,UAAU,GAAG,IAAI;QACjBK,UAAU,CAAC;UAAA,OAAML,UAAU,GAAG,KAAK;QAAA,GAAED,KAAK,CAAC;MAC7C;IACF,CAAC;EACH;EAEA,SAASO,YAAY,GAAG;IACtB,IAAMC,cAAc,GAAGZ,MAAM,CAACC,OAAO;IACrC,IAAMY,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAACH,cAAc,GAAGb,WAAW,CAAC;;IAE/D;IACA,IAAIc,gBAAgB,GAAG,CAAC,EAAE;IAE1B,IAAID,cAAc,IAAI,GAAG,EAAE;MACzB;MACAhB,MAAM,CAACoB,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MACrCrB,MAAM,CAACoB,SAAS,CAACE,GAAG,CAAC,aAAa,CAAC;IACrC,CAAC,MAAM,IAAIN,cAAc,GAAGb,WAAW,IAAIa,cAAc,GAAG,GAAG,EAAE;MAC/D;MACAhB,MAAM,CAACoB,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;MAClCtB,MAAM,CAACoB,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;IACxC,CAAC,MAAM,IAAIL,cAAc,GAAGb,WAAW,EAAE;MACvC;MACAH,MAAM,CAACoB,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;MACrCrB,MAAM,CAACoB,SAAS,CAACE,GAAG,CAAC,aAAa,CAAC;IACrC;IAEAnB,WAAW,GAAGa,cAAc;EAC9B;;EAEA;EACA,IAAMO,sBAAsB,GAAGjB,QAAQ,CAACS,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;;EAE3D;EACAX,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAED,sBAAsB,EAAE;IAAEE,OAAO,EAAE;EAAK,CAAC,CAAC;;EAE5E;EACAzB,MAAM,CAACoB,SAAS,CAACE,GAAG,CAAC,aAAa,CAAC;AACrC;;AAEA;AACO,SAASI,sBAAsB,GAAG;EACvC,IAAMC,QAAQ,GAAG1B,QAAQ,CAAC2B,gBAAgB,CAAC,WAAW,CAAC;EACvD,IAAI,CAACD,QAAQ,CAACE,MAAM,EAAE;;EAEtB;EACA,SAASC,iBAAiB,CAACC,KAAK,EAAE;IAChCA,KAAK,CAACC,cAAc,EAAE;IACtBD,KAAK,CAACE,eAAe,EAAE;IAEvB,IAAMC,MAAM,GAAGH,KAAK,CAACI,aAAa;IAClC,IAAMC,OAAO,GAAGF,MAAM,CAACG,OAAO,CAAC,+BAA+B,CAAC;IAC/D,IAAMC,UAAU,GAAGJ,MAAM,CAACK,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;;IAElE;IACA,IAAMC,WAAW,GAAGJ,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,QAAQ;IAC1E,IAAIG,WAAW,KAAK,QAAQ,EAAE;MAC5B;MACAvC,QAAQ,CAAC2B,gBAAgB,CAAC,WAAW,CAAC,CAACa,OAAO,CAAC,UAAAC,IAAI,EAAI;QACrD,IAAIA,IAAI,KAAKN,OAAO,EAAE;UACpBO,aAAa,CAACD,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAME,cAAc,GAAGR,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC;MACvDO,cAAc,CAAChB,gBAAgB,CAAC,oBAAoB,CAAC,CAACa,OAAO,CAAC,UAAAC,IAAI,EAAI;QACpE,IAAIA,IAAI,KAAKN,OAAO,EAAE;UACpBO,aAAa,CAACD,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIJ,UAAU,EAAE;MACdK,aAAa,CAACP,OAAO,CAAC;IACxB,CAAC,MAAM;MACLS,YAAY,CAACT,OAAO,CAAC;IACvB;EACF;;EAEA;EACA,SAASS,YAAY,CAACT,OAAO,EAAE;IAC7B,IAAMF,MAAM,GAAGE,OAAO,CAAClC,aAAa,CAAC,iCAAiC,CAAC;IACvE,IAAIgC,MAAM,EAAE;MACVA,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MAC5CV,OAAO,CAAChB,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;IACrC;EACF;;EAEA;EACA,SAASqB,aAAa,CAACP,OAAO,EAAE;IAC9B,IAAMF,MAAM,GAAGE,OAAO,CAAClC,aAAa,CAAC,iCAAiC,CAAC;IACvE,IAAIgC,MAAM,EAAE;MACVA,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MAC7CV,OAAO,CAAChB,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;;MAEtC;MACAe,OAAO,CAACR,gBAAgB,CAAC,oBAAoB,CAAC,CAACa,OAAO,CAAC,UAAAM,SAAS,EAAI;QAClEJ,aAAa,CAACI,SAAS,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF;;EAEA;EACA,SAASC,iBAAiB,GAAG;IAC3BrB,QAAQ,CAACc,OAAO,CAAC,UAAAL,OAAO,EAAI;MAC1BO,aAAa,CAACP,OAAO,CAAC;IACxB,CAAC,CAAC;EACJ;;EAEA;EACA,SAASa,aAAa,CAAClB,KAAK,EAAE;IAC5B,IAAQmB,GAAG,GAAKnB,KAAK,CAAbmB,GAAG;IACX,IAAMC,aAAa,GAAGlD,QAAQ,CAACkD,aAAa;IAE5C,IAAID,GAAG,KAAK,QAAQ,EAAE;MACpBF,iBAAiB,EAAE;MACnB;MACA,IAAIG,aAAa,CAACd,OAAO,CAAC,eAAe,CAAC,EAAE;QAC1C,IAAMe,WAAW,GAAGnD,QAAQ,CAACC,aAAa,CAAC,aAAa,CAAC;QACzD,IAAIkD,WAAW,EAAEA,WAAW,CAACC,KAAK,EAAE;MACtC;MACA;IACF;;IAEA;IACA,IAAIF,aAAa,CAACd,OAAO,CAAC,eAAe,CAAC,EAAE;MAC1C,IAAMiB,QAAQ,GAAGH,aAAa,CAACd,OAAO,CAAC,eAAe,CAAC;MACvD,IAAMkB,iBAAiB,GAAGD,QAAQ,CAAC1B,gBAAgB,CAAC,wCAAwC,CAAC;MAC7F,IAAM4B,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACH,iBAAiB,CAAC,CAACI,OAAO,CAACR,aAAa,CAAC;MAEzE,IAAID,GAAG,KAAK,WAAW,EAAE;QACvBnB,KAAK,CAACC,cAAc,EAAE;QACtB,IAAM4B,SAAS,GAAG,CAACJ,YAAY,GAAG,CAAC,IAAID,iBAAiB,CAAC1B,MAAM;QAC/D0B,iBAAiB,CAACK,SAAS,CAAC,CAACP,KAAK,EAAE;MACtC,CAAC,MAAM,IAAIH,GAAG,KAAK,SAAS,EAAE;QAC5BnB,KAAK,CAACC,cAAc,EAAE;QACtB,IAAM6B,SAAS,GAAGL,YAAY,KAAK,CAAC,GAAGD,iBAAiB,CAAC1B,MAAM,GAAG,CAAC,GAAG2B,YAAY,GAAG,CAAC;QACtFD,iBAAiB,CAACM,SAAS,CAAC,CAACR,KAAK,EAAE;MACtC;IACF;EACF;;EAEA;EACA1B,QAAQ,CAACc,OAAO,CAAC,UAAAL,OAAO,EAAI;IAC1B,IAAMF,MAAM,GAAGE,OAAO,CAAClC,aAAa,CAAC,iCAAiC,CAAC;IACvE,IAAIgC,MAAM,EAAE;MACVA,MAAM,CAACV,gBAAgB,CAAC,OAAO,EAAEM,iBAAiB,CAAC;IACrD;EACF,CAAC,CAAC;;EAEF;EACA7B,QAAQ,CAACuB,gBAAgB,CAAC,OAAO,EAAE,UAACO,KAAK,EAAK;IAC5C,IAAI,CAACA,KAAK,CAAC+B,MAAM,CAACzB,OAAO,CAAC,WAAW,CAAC,EAAE;MACtCW,iBAAiB,EAAE;IACrB;EACF,CAAC,CAAC;;EAEF;EACA/C,QAAQ,CAACuB,gBAAgB,CAAC,SAAS,EAAEyB,aAAa,CAAC;;EAEnD;EACAhD,QAAQ,CAACuB,gBAAgB,CAAC,UAAU,EAAE,YAAM;IAC1C;IACAV,UAAU,CAAC,YAAM;MACf,IAAMqC,aAAa,GAAGlD,QAAQ,CAACkD,aAAa;MAC5C,IAAI,CAACA,aAAa,CAACd,OAAO,CAAC,iBAAiB,CAAC,EAAE;QAC7CW,iBAAiB,EAAE;MACrB;IACF,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,oBAAoB,GAAG;EACrC,IAAMC,aAAa,GAAG/D,QAAQ,CAAC2B,gBAAgB,CAAC,oBAAoB,CAAC;EACrE,IAAMqC,GAAG,GAAGhE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzC,IAAMgE,eAAe,GAAGjE,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;EACnE,IAAMiE,aAAa,GAAGlE,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;EAEnE,IAAI,CAAC8D,aAAa,CAACnC,MAAM,IAAI,CAACoC,GAAG,IAAI,CAACC,eAAe,IAAI,CAACC,aAAa,EAAE;;EAEzE;EACA,SAASC,gBAAgB,GAAG;IAC1B,IAAM9B,UAAU,GAAG0B,aAAa,CAAC,CAAC,CAAC,CAACzB,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;IAE5E,IAAID,UAAU,EAAE;MACd+B,eAAe,EAAE;IACnB,CAAC,MAAM;MACLC,cAAc,EAAE;IAClB;EACF;;EAEA;EACA,SAASA,cAAc,GAAG;IACxBL,GAAG,CAAC7C,SAAS,CAACE,GAAG,CAAC,qBAAqB,CAAC;IACxC4C,eAAe,CAAC9C,SAAS,CAACE,GAAG,CAAC,cAAc,CAAC;;IAE7C;IACA0C,aAAa,CAACvB,OAAO,CAAC,UAAAP,MAAM,EAAI;MAC9BA,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;MAC5CZ,MAAM,CAACY,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA7C,QAAQ,CAACsE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;;IAEvC;IACAC,qBAAqB,EAAE;;IAEvB;IACA,IAAMC,cAAc,GAAGR,aAAa,CAACjE,aAAa,CAAC,WAAW,CAAC;IAC/D,IAAIyE,cAAc,EAAE;MAClB7D,UAAU,CAAC;QAAA,OAAM6D,cAAc,CAACtB,KAAK,EAAE;MAAA,GAAE,GAAG,CAAC;IAC/C;EACF;;EAEA;EACA,SAASgB,eAAe,GAAG;IACzBJ,GAAG,CAAC7C,SAAS,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC3C6C,eAAe,CAAC9C,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;;IAEhD;IACA2C,aAAa,CAACvB,OAAO,CAAC,UAAAP,MAAM,EAAI;MAC9BA,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;MAC7CZ,MAAM,CAACY,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA7C,QAAQ,CAACsE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;;IAEjC;IACA,IAAMG,WAAW,GAAGT,aAAa,CAACvC,gBAAgB,CAAC,uCAAuC,CAAC;IAC3FgD,WAAW,CAACnC,OAAO,CAAC,UAAAC,IAAI,EAAI;MAC1B,IAAMR,MAAM,GAAGQ,IAAI,CAACxC,aAAa,CAAC,qDAAqD,CAAC;MACxF,IAAM2E,OAAO,GAAGnC,IAAI,CAACxC,aAAa,CAAC,6CAA6C,CAAC;MACjF,IAAIgC,MAAM,IAAI2C,OAAO,EAAE;QACrB3C,MAAM,CAACY,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;QAC7C+B,OAAO,CAACzD,SAAS,CAACC,MAAM,CAAC,2BAA2B,CAAC;MACvD;IACF,CAAC,CAAC;;IAEF;IACA2C,aAAa,CAAC,CAAC,CAAC,CAACX,KAAK,EAAE;EAC1B;;EAEA;EACA,SAASqB,qBAAqB,GAAG;IAC/B,IAAMI,KAAK,GAAGX,aAAa,CAACvC,gBAAgB,CAAC,mCAAmC,CAAC;IACjFkD,KAAK,CAACrC,OAAO,CAAC,UAAAC,IAAI,EAAI;MACpBA,IAAI,CAAC8B,KAAK,CAACO,SAAS,GAAG,MAAM;MAC7BrC,IAAI,CAACsC,YAAY,CAAC,CAAC;MACnBtC,IAAI,CAAC8B,KAAK,CAACO,SAAS,GAAG,IAAI;IAC7B,CAAC,CAAC;EACJ;;EAIA;EACA,SAASE,qBAAqB,GAAG;IAC/B;IACAZ,eAAe,EAAE;EACnB;;EAEA;EACAL,aAAa,CAACvB,OAAO,CAAC,UAAAP,MAAM,EAAI;IAC9BA,MAAM,CAACV,gBAAgB,CAAC,OAAO,EAAE4C,gBAAgB,CAAC;EACpD,CAAC,CAAC;;EAEF;EACAD,aAAa,CAAC3C,gBAAgB,CAAC,OAAO,EAAE,UAACO,KAAK,EAAK;IACjD,IAAMmD,YAAY,GAAGnD,KAAK,CAAC+B,MAAM,CAACzB,OAAO,CAAC,qDAAqD,CAAC;IAChG,IAAI6C,YAAY,EAAE;MAChBnD,KAAK,CAACC,cAAc,EAAE;MACtBD,KAAK,CAACE,eAAe,EAAE;MAEvB,IAAMkD,UAAU,GAAGD,YAAY,CAAC7C,OAAO,CAAC,uCAAuC,CAAC;MAChF,IAAMwC,OAAO,GAAGM,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjF,aAAa,CAAC,6CAA6C,CAAC;MACxF,IAAMoC,UAAU,GAAG4C,YAAY,CAAC3C,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MAExE,IAAIsC,OAAO,EAAE;QACX,IAAIvC,UAAU,EAAE;UACd4C,YAAY,CAACpC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;UACnD+B,OAAO,CAACzD,SAAS,CAACC,MAAM,CAAC,2BAA2B,CAAC;QACvD,CAAC,MAAM;UACL6D,YAAY,CAACpC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;UAClD+B,OAAO,CAACzD,SAAS,CAACE,GAAG,CAAC,2BAA2B,CAAC;QACpD;MACF;IACF;EACF,CAAC,CAAC;;EAEF;EACA,IAAM8D,WAAW,GAAGnF,QAAQ,CAAC2B,gBAAgB,CAAC,0CAA0C,CAAC;EACzFwD,WAAW,CAAC3C,OAAO,CAAC,UAAA4C,IAAI,EAAI;IAC1BA,IAAI,CAAC7D,gBAAgB,CAAC,OAAO,EAAEyD,qBAAqB,CAAC;EACvD,CAAC,CAAC;;EAEF;EACAhF,QAAQ,CAACuB,gBAAgB,CAAC,OAAO,EAAE,UAACO,KAAK,EAAK;IAC5C,IAAIkC,GAAG,CAAC7C,SAAS,CAACkE,QAAQ,CAAC,qBAAqB,CAAC,IAC7C,CAACrB,GAAG,CAACqB,QAAQ,CAACvD,KAAK,CAAC+B,MAAM,CAAC,EAAE;MAC/B;MACA,IAAMyB,aAAa,GAAG9B,KAAK,CAACC,IAAI,CAACM,aAAa,CAAC,CAACwB,IAAI,CAAC,UAAAtD,MAAM;QAAA,OACzDA,MAAM,CAACoD,QAAQ,CAACvD,KAAK,CAAC+B,MAAM,CAAC;MAAA,EAC9B;MACD,IAAI,CAACyB,aAAa,EAAE;QAClBlB,eAAe,EAAE;MACnB;IACF;EACF,CAAC,CAAC;;EAEF;EACApE,QAAQ,CAACuB,gBAAgB,CAAC,SAAS,EAAE,UAACO,KAAK,EAAK;IAC9C,IAAIA,KAAK,CAACmB,GAAG,KAAK,QAAQ,IAAIe,GAAG,CAAC7C,SAAS,CAACkE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAC3EjB,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;;EAEF;EACAjE,MAAM,CAACoB,gBAAgB,CAAC,QAAQ,EAAE,YAAM;IACtC,IAAIpB,MAAM,CAACqF,UAAU,GAAG,IAAI,IAAIxB,GAAG,CAAC7C,SAAS,CAACkE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;MAC7EjB,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;AACJ;;;;;;;;;;;;AC1VA;AACA;AACA;AACA;;AAEyG;;AAEzG;AACA,SAASqB,KAAK,CAACC,QAAQ,EAAE;EACvB,IAAI1F,QAAQ,CAAC2F,UAAU,KAAK,SAAS,EAAE;IACrC3F,QAAQ,CAACuB,gBAAgB,CAAC,kBAAkB,EAAEmE,QAAQ,CAAC;EACzD,CAAC,MAAM;IACLA,QAAQ,EAAE;EACZ;AACF;;AAEA;AACAD,KAAK,CAAC,YAAM;EACVG,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;;EAEpC;EACA/F,wEAAoB,EAAE;;EAEtB;EACA2B,0EAAsB,EAAE;;EAExB;EACAqC,wEAAoB,EAAE;AACxB,CAAC,CAAC;;;;;;;;;;;AC5BF;;;;;;;;;;;;ACAA;;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UElDA;UACA;UACA;UACA;UACA;UACA;UACA", "sources": ["webpack:///./resources/js/components/nav.js", "webpack:///./resources/js/site.js", "webpack:///./resources/css/style.scss", "webpack:///./resources/css/tailwind.css", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/chunk loaded", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///webpack/runtime/jsonp chunk loading", "webpack:///webpack/before-startup", "webpack:///webpack/startup", "webpack:///webpack/after-startup"], "sourcesContent": ["/**\n * Navigation Component\n * Handles scroll-based navigation behavior and dropdown interactions for OK Tyr website\n */\n\n// Scroll Navigation Handler\nexport function initScrollNavigation() {\n  const navbar = document.querySelector('.navbar-container');\n  if (!navbar) return;\n\n  let lastScrollY = window.scrollY;\n\n  // Throttle scroll events for better performance\n  function throttle(func, limit) {\n    let inThrottle;\n    return function() {\n      const args = arguments;\n      const context = this;\n      if (!inThrottle) {\n        func.apply(context, args);\n        inThrottle = true;\n        setTimeout(() => inThrottle = false, limit);\n      }\n    }\n  }\n\n  function handleScroll() {\n    const currentScrollY = window.scrollY;\n    const scrollDifference = Math.abs(currentScrollY - lastScrollY);\n\n    // Only react to significant scroll movements (avoid jitter)\n    if (scrollDifference < 5) return;\n\n    if (currentScrollY <= 100) {\n      // Always show nav when near top of page\n      navbar.classList.remove('nav-hidden');\n      navbar.classList.add('nav-visible');\n    } else if (currentScrollY > lastScrollY && currentScrollY > 200) {\n      // Scrolling down - hide nav\n      navbar.classList.add('nav-hidden');\n      navbar.classList.remove('nav-visible');\n    } else if (currentScrollY < lastScrollY) {\n      // Scrolling up - show nav\n      navbar.classList.remove('nav-hidden');\n      navbar.classList.add('nav-visible');\n    }\n\n    lastScrollY = currentScrollY;\n  }\n\n  // Use throttled scroll handler\n  const throttledScrollHandler = throttle(handleScroll, 16); // ~60fps\n\n  // Add scroll listener\n  window.addEventListener('scroll', throttledScrollHandler, { passive: true });\n\n  // Initialize nav state\n  navbar.classList.add('nav-visible');\n}\n\n// Dropdown Navigation Handler\nexport function initDropdownNavigation() {\n  const navItems = document.querySelectorAll('.nav-item');\n  if (!navItems.length) return;\n\n  // Handle click events for dropdown toggles\n  function handleToggleClick(event) {\n    event.preventDefault();\n    event.stopPropagation();\n\n    const toggle = event.currentTarget;\n    const navItem = toggle.closest('.nav-item, .nav-dropdown-item');\n    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';\n\n    // Close all other dropdowns at the same level\n    const parentLevel = navItem.closest('.nav-dropdown') ? 'level2' : 'level1';\n    if (parentLevel === 'level1') {\n      // Close all level 1 dropdowns\n      document.querySelectorAll('.nav-item').forEach(item => {\n        if (item !== navItem) {\n          closeDropdown(item);\n        }\n      });\n    } else {\n      // Close all level 2 dropdowns in the same parent\n      const parentDropdown = navItem.closest('.nav-dropdown');\n      parentDropdown.querySelectorAll('.nav-dropdown-item').forEach(item => {\n        if (item !== navItem) {\n          closeDropdown(item);\n        }\n      });\n    }\n\n    // Toggle current dropdown\n    if (isExpanded) {\n      closeDropdown(navItem);\n    } else {\n      openDropdown(navItem);\n    }\n  }\n\n  // Open dropdown\n  function openDropdown(navItem) {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.setAttribute('aria-expanded', 'true');\n      navItem.classList.add('nav-active');\n    }\n  }\n\n  // Close dropdown\n  function closeDropdown(navItem) {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.setAttribute('aria-expanded', 'false');\n      navItem.classList.remove('nav-active');\n\n      // Also close any child dropdowns\n      navItem.querySelectorAll('.nav-dropdown-item').forEach(childItem => {\n        closeDropdown(childItem);\n      });\n    }\n  }\n\n  // Close all dropdowns\n  function closeAllDropdowns() {\n    navItems.forEach(navItem => {\n      closeDropdown(navItem);\n    });\n  }\n\n  // Handle keyboard navigation\n  function handleKeyDown(event) {\n    const { key } = event;\n    const activeElement = document.activeElement;\n\n    if (key === 'Escape') {\n      closeAllDropdowns();\n      // Focus the first nav toggle if we were in a dropdown\n      if (activeElement.closest('.nav-dropdown')) {\n        const firstToggle = document.querySelector('.nav-toggle');\n        if (firstToggle) firstToggle.focus();\n      }\n      return;\n    }\n\n    // Arrow key navigation within dropdowns\n    if (activeElement.closest('.nav-dropdown')) {\n      const dropdown = activeElement.closest('.nav-dropdown');\n      const focusableElements = dropdown.querySelectorAll('.nav-dropdown-link, .nav-toggle-level2');\n      const currentIndex = Array.from(focusableElements).indexOf(activeElement);\n\n      if (key === 'ArrowDown') {\n        event.preventDefault();\n        const nextIndex = (currentIndex + 1) % focusableElements.length;\n        focusableElements[nextIndex].focus();\n      } else if (key === 'ArrowUp') {\n        event.preventDefault();\n        const prevIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;\n        focusableElements[prevIndex].focus();\n      }\n    }\n  }\n\n  // Add event listeners\n  navItems.forEach(navItem => {\n    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');\n    if (toggle) {\n      toggle.addEventListener('click', handleToggleClick);\n    }\n  });\n\n  // Close dropdowns when clicking outside\n  document.addEventListener('click', (event) => {\n    if (!event.target.closest('.nav-item')) {\n      closeAllDropdowns();\n    }\n  });\n\n  // Handle keyboard navigation\n  document.addEventListener('keydown', handleKeyDown);\n\n  // Handle focus loss (for accessibility)\n  document.addEventListener('focusout', () => {\n    // Small delay to check if focus moved to another nav element\n    setTimeout(() => {\n      const activeElement = document.activeElement;\n      if (!activeElement.closest('.nav-collection')) {\n        closeAllDropdowns();\n      }\n    }, 100);\n  });\n}\n\n// Mobile Navigation Handler\nexport function initMobileNavigation() {\n  const mobileToggles = document.querySelectorAll('.nav-mobile-toggle');\n  const nav = document.querySelector('nav');\n  const navbarContainer = document.querySelector('.navbar-container');\n  const mobileContent = document.querySelector('.nav-mobile-content');\n\n  if (!mobileToggles.length || !nav || !navbarContainer || !mobileContent) return;\n\n  // Toggle mobile menu\n  function toggleMobileMenu() {\n    const isExpanded = mobileToggles[0].getAttribute('aria-expanded') === 'true';\n\n    if (isExpanded) {\n      closeMobileMenu();\n    } else {\n      openMobileMenu();\n    }\n  }\n\n  // Open mobile menu (expand navbar)\n  function openMobileMenu() {\n    nav.classList.add('nav-mobile-expanded');\n    navbarContainer.classList.add('nav-expanded');\n\n    // Update all toggle buttons\n    mobileToggles.forEach(toggle => {\n      toggle.setAttribute('aria-expanded', 'true');\n      toggle.setAttribute('aria-label', 'Stäng meny');\n    });\n\n    // Prevent body scroll\n    document.body.style.overflow = 'hidden';\n\n    // Reset animations for menu items\n    resetMobileAnimations();\n\n    // Focus management\n    const firstFocusable = mobileContent.querySelector('a, button');\n    if (firstFocusable) {\n      setTimeout(() => firstFocusable.focus(), 300);\n    }\n  }\n\n  // Close mobile menu (collapse navbar)\n  function closeMobileMenu() {\n    nav.classList.remove('nav-mobile-expanded');\n    navbarContainer.classList.remove('nav-expanded');\n\n    // Update all toggle buttons\n    mobileToggles.forEach(toggle => {\n      toggle.setAttribute('aria-expanded', 'false');\n      toggle.setAttribute('aria-label', 'Öppna meny');\n    });\n\n    // Restore body scroll\n    document.body.style.overflow = '';\n\n    // Close all submenus\n    const mobileItems = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-subitem');\n    mobileItems.forEach(item => {\n      const toggle = item.querySelector('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');\n      const submenu = item.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');\n      if (toggle && submenu) {\n        toggle.setAttribute('aria-expanded', 'false');\n        submenu.classList.remove('nav-mobile-submenu-active');\n      }\n    });\n\n    // Return focus to first toggle button\n    mobileToggles[0].focus();\n  }\n\n  // Reset mobile menu animations\n  function resetMobileAnimations() {\n    const items = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-cta');\n    items.forEach(item => {\n      item.style.animation = 'none';\n      item.offsetHeight; // Trigger reflow\n      item.style.animation = null;\n    });\n  }\n\n\n\n  // Handle mobile menu link clicks\n  function handleMobileLinkClick() {\n    // Close mobile menu when a link is clicked\n    closeMobileMenu();\n  }\n\n  // Event listeners - add click listener to all toggle buttons\n  mobileToggles.forEach(toggle => {\n    toggle.addEventListener('click', toggleMobileMenu);\n  });\n\n  // Add submenu toggle listeners (using event delegation for dynamic content)\n  mobileContent.addEventListener('click', (event) => {\n    const toggleButton = event.target.closest('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');\n    if (toggleButton) {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const parentItem = toggleButton.closest('.nav-mobile-item, .nav-mobile-subitem');\n      const submenu = parentItem?.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');\n      const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';\n\n      if (submenu) {\n        if (isExpanded) {\n          toggleButton.setAttribute('aria-expanded', 'false');\n          submenu.classList.remove('nav-mobile-submenu-active');\n        } else {\n          toggleButton.setAttribute('aria-expanded', 'true');\n          submenu.classList.add('nav-mobile-submenu-active');\n        }\n      }\n    }\n  });\n\n  // Add link click listeners to close menu\n  const mobileLinks = document.querySelectorAll('.nav-mobile-link, .nav-mobile-cta-button');\n  mobileLinks.forEach(link => {\n    link.addEventListener('click', handleMobileLinkClick);\n  });\n\n  // Close menu when clicking outside content area\n  document.addEventListener('click', (event) => {\n    if (nav.classList.contains('nav-mobile-expanded') &&\n        !nav.contains(event.target)) {\n      // Check if click was on any toggle button\n      const clickedToggle = Array.from(mobileToggles).some(toggle =>\n        toggle.contains(event.target)\n      );\n      if (!clickedToggle) {\n        closeMobileMenu();\n      }\n    }\n  });\n\n  // Handle escape key\n  document.addEventListener('keydown', (event) => {\n    if (event.key === 'Escape' && nav.classList.contains('nav-mobile-expanded')) {\n      closeMobileMenu();\n    }\n  });\n\n  // Handle window resize to close mobile menu on desktop\n  window.addEventListener('resize', () => {\n    if (window.innerWidth > 1024 && nav.classList.contains('nav-mobile-expanded')) {\n      closeMobileMenu();\n    }\n  });\n}", "/**\n * Main Site JavaScript\n * Minimal setup for OK Tyr website\n */\n\nimport { initScrollNavigation, initDropdownNavigation, initMobileNavigation } from './components/nav.js';\n\n// Simple DOM ready function\nfunction ready(callback) {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback);\n  } else {\n    callback();\n  }\n}\n\n// Initialize when DOM is ready\nready(() => {\n  console.log('OK Tyr website loaded');\n\n  // Initialize scroll navigation\n  initScrollNavigation();\n\n  // Initialize dropdown navigation\n  initDropdownNavigation();\n\n  // Initialize mobile navigation\n  initMobileNavigation();\n});\n", "// extracted by mini-css-extract-plugin\nexport {};", "// extracted by mini-css-extract-plugin\nexport {};", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"/js/site\": 0,\n\t\"css/tailwind\": 0,\n\t\"css/style\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunk\"] = self[\"webpackChunk\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\n__webpack_require__.O(undefined, [\"css/tailwind\",\"css/style\"], () => (__webpack_require__(\"./resources/js/site.js\")))\n__webpack_require__.O(undefined, [\"css/tailwind\",\"css/style\"], () => (__webpack_require__(\"./resources/css/style.scss\")))\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"css/tailwind\",\"css/style\"], () => (__webpack_require__(\"./resources/css/tailwind.css\")))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["initScrollNavigation", "navbar", "document", "querySelector", "lastScrollY", "window", "scrollY", "throttle", "func", "limit", "inThrottle", "args", "arguments", "context", "apply", "setTimeout", "handleScroll", "currentScrollY", "scrollDifference", "Math", "abs", "classList", "remove", "add", "throttledScrollHandler", "addEventListener", "passive", "initDropdownNavigation", "navItems", "querySelectorAll", "length", "handleToggleClick", "event", "preventDefault", "stopPropagation", "toggle", "currentTarget", "navItem", "closest", "isExpanded", "getAttribute", "parentLevel", "for<PERSON>ach", "item", "closeDropdown", "parentDropdown", "openDropdown", "setAttribute", "childItem", "closeAllDropdowns", "handleKeyDown", "key", "activeElement", "firstToggle", "focus", "dropdown", "focusableElements", "currentIndex", "Array", "from", "indexOf", "nextIndex", "prevIndex", "target", "initMobileNavigation", "mobileToggles", "nav", "navbarContainer", "mobileContent", "toggleMobileMenu", "closeMobileMenu", "openMobileMenu", "body", "style", "overflow", "resetMobileAnimations", "firstFocusable", "mobileItems", "submenu", "items", "animation", "offsetHeight", "handleMobileLinkClick", "to<PERSON><PERSON><PERSON><PERSON>", "parentItem", "mobileLinks", "link", "contains", "clickedToggle", "some", "innerWidth", "ready", "callback", "readyState", "console", "log"], "sourceRoot": ""}