/**
 * Navigation Component
 * Handles scroll-based navigation behavior and dropdown interactions for OK Tyr website
 */

// Scroll Navigation Handler
export function initScrollNavigation() {
  const navbar = document.querySelector('.navbar-container');
  if (!navbar) return;

  let lastScrollY = window.scrollY;

  // Throttle scroll events for better performance
  function throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }

  function handleScroll() {
    const currentScrollY = window.scrollY;
    const scrollDifference = Math.abs(currentScrollY - lastScrollY);

    // Only react to significant scroll movements (avoid jitter)
    if (scrollDifference < 5) return;

    if (currentScrollY <= 100) {
      // Always show nav when near top of page
      navbar.classList.remove('nav-hidden');
      navbar.classList.add('nav-visible');
    } else if (currentScrollY > lastScrollY && currentScrollY > 200) {
      // Scrolling down - hide nav
      navbar.classList.add('nav-hidden');
      navbar.classList.remove('nav-visible');
    } else if (currentScrollY < lastScrollY) {
      // Scrolling up - show nav
      navbar.classList.remove('nav-hidden');
      navbar.classList.add('nav-visible');
    }

    lastScrollY = currentScrollY;
  }

  // Use throttled scroll handler
  const throttledScrollHandler = throttle(handleScroll, 16); // ~60fps

  // Add scroll listener
  window.addEventListener('scroll', throttledScrollHandler, { passive: true });

  // Initialize nav state
  navbar.classList.add('nav-visible');
}

// Dropdown Navigation Handler
export function initDropdownNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  if (!navItems.length) return;

  // Handle click events for dropdown toggles
  function handleToggleClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const toggle = event.currentTarget;
    const navItem = toggle.closest('.nav-item, .nav-dropdown-item');
    const isExpanded = toggle.getAttribute('aria-expanded') === 'true';

    // Close all other dropdowns at the same level
    const parentLevel = navItem.closest('.nav-dropdown') ? 'level2' : 'level1';
    if (parentLevel === 'level1') {
      // Close all level 1 dropdowns
      document.querySelectorAll('.nav-item').forEach(item => {
        if (item !== navItem) {
          closeDropdown(item);
        }
      });
    } else {
      // Close all level 2 dropdowns in the same parent
      const parentDropdown = navItem.closest('.nav-dropdown');
      parentDropdown.querySelectorAll('.nav-dropdown-item').forEach(item => {
        if (item !== navItem) {
          closeDropdown(item);
        }
      });
    }

    // Toggle current dropdown
    if (isExpanded) {
      closeDropdown(navItem);
    } else {
      openDropdown(navItem);
    }
  }

  // Open dropdown
  function openDropdown(navItem) {
    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');
    if (toggle) {
      toggle.setAttribute('aria-expanded', 'true');
      navItem.classList.add('nav-active');
    }
  }

  // Close dropdown
  function closeDropdown(navItem) {
    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');
    if (toggle) {
      toggle.setAttribute('aria-expanded', 'false');
      navItem.classList.remove('nav-active');

      // Also close any child dropdowns
      navItem.querySelectorAll('.nav-dropdown-item').forEach(childItem => {
        closeDropdown(childItem);
      });
    }
  }

  // Close all dropdowns
  function closeAllDropdowns() {
    navItems.forEach(navItem => {
      closeDropdown(navItem);
    });
  }

  // Handle keyboard navigation
  function handleKeyDown(event) {
    const { key } = event;
    const activeElement = document.activeElement;

    if (key === 'Escape') {
      closeAllDropdowns();
      // Focus the first nav toggle if we were in a dropdown
      if (activeElement.closest('.nav-dropdown')) {
        const firstToggle = document.querySelector('.nav-toggle');
        if (firstToggle) firstToggle.focus();
      }
      return;
    }

    // Arrow key navigation within dropdowns
    if (activeElement.closest('.nav-dropdown')) {
      const dropdown = activeElement.closest('.nav-dropdown');
      const focusableElements = dropdown.querySelectorAll('.nav-dropdown-link, .nav-toggle-level2');
      const currentIndex = Array.from(focusableElements).indexOf(activeElement);

      if (key === 'ArrowDown') {
        event.preventDefault();
        const nextIndex = (currentIndex + 1) % focusableElements.length;
        focusableElements[nextIndex].focus();
      } else if (key === 'ArrowUp') {
        event.preventDefault();
        const prevIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;
        focusableElements[prevIndex].focus();
      }
    }
  }

  // Add event listeners
  navItems.forEach(navItem => {
    const toggle = navItem.querySelector('.nav-toggle, .nav-toggle-level2');
    if (toggle) {
      toggle.addEventListener('click', handleToggleClick);
    }
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', (event) => {
    if (!event.target.closest('.nav-item')) {
      closeAllDropdowns();
    }
  });

  // Handle keyboard navigation
  document.addEventListener('keydown', handleKeyDown);

  // Handle focus loss (for accessibility)
  document.addEventListener('focusout', () => {
    // Small delay to check if focus moved to another nav element
    setTimeout(() => {
      const activeElement = document.activeElement;
      if (!activeElement.closest('.nav-collection')) {
        closeAllDropdowns();
      }
    }, 100);
  });
}

// Mobile Navigation Handler
export function initMobileNavigation() {
  const mobileToggles = document.querySelectorAll('.nav-mobile-toggle');
  const nav = document.querySelector('nav');
  const navbarContainer = document.querySelector('.navbar-container');
  const mobileContent = document.querySelector('.nav-mobile-content');

  if (!mobileToggles.length || !nav || !navbarContainer || !mobileContent) return;

  // Toggle mobile menu
  function toggleMobileMenu() {
    const isExpanded = mobileToggles[0].getAttribute('aria-expanded') === 'true';

    if (isExpanded) {
      closeMobileMenu();
    } else {
      openMobileMenu();
    }
  }

  // Open mobile menu (expand navbar)
  function openMobileMenu() {
    nav.classList.add('nav-mobile-expanded');
    navbarContainer.classList.add('nav-expanded');

    // Update all toggle buttons
    mobileToggles.forEach(toggle => {
      toggle.setAttribute('aria-expanded', 'true');
      toggle.setAttribute('aria-label', 'Stäng meny');
    });

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Reset animations for menu items
    resetMobileAnimations();

    // Focus management
    const firstFocusable = mobileContent.querySelector('a, button');
    if (firstFocusable) {
      setTimeout(() => firstFocusable.focus(), 300);
    }
  }

  // Close mobile menu (collapse navbar)
  function closeMobileMenu() {
    nav.classList.remove('nav-mobile-expanded');
    navbarContainer.classList.remove('nav-expanded');

    // Update all toggle buttons
    mobileToggles.forEach(toggle => {
      toggle.setAttribute('aria-expanded', 'false');
      toggle.setAttribute('aria-label', 'Öppna meny');
    });

    // Restore body scroll
    document.body.style.overflow = '';

    // Close all submenus
    const mobileItems = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-subitem');
    mobileItems.forEach(item => {
      const toggle = item.querySelector('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');
      const submenu = item.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');
      if (toggle && submenu) {
        toggle.setAttribute('aria-expanded', 'false');
        submenu.classList.remove('nav-mobile-submenu-active');
      }
    });

    // Return focus to first toggle button
    mobileToggles[0].focus();
  }

  // Reset mobile menu animations
  function resetMobileAnimations() {
    const items = mobileContent.querySelectorAll('.nav-mobile-item, .nav-mobile-cta');
    items.forEach(item => {
      item.style.animation = 'none';
      item.offsetHeight; // Trigger reflow
      item.style.animation = null;
    });
  }



  // Handle mobile menu link clicks
  function handleMobileLinkClick() {
    // Close mobile menu when a link is clicked
    closeMobileMenu();
  }

  // Event listeners - add click listener to all toggle buttons
  mobileToggles.forEach(toggle => {
    toggle.addEventListener('click', toggleMobileMenu);
  });

  // Add submenu toggle listeners (using event delegation for dynamic content)
  mobileContent.addEventListener('click', (event) => {
    const toggleButton = event.target.closest('.nav-mobile-toggle-item, .nav-mobile-toggle-subitem');
    if (toggleButton) {
      event.preventDefault();
      event.stopPropagation();

      const parentItem = toggleButton.closest('.nav-mobile-item, .nav-mobile-subitem');
      const submenu = parentItem?.querySelector('.nav-mobile-submenu, .nav-mobile-subsubmenu');
      const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';

      if (submenu) {
        if (isExpanded) {
          toggleButton.setAttribute('aria-expanded', 'false');
          submenu.classList.remove('nav-mobile-submenu-active');
        } else {
          toggleButton.setAttribute('aria-expanded', 'true');
          submenu.classList.add('nav-mobile-submenu-active');
        }
      }
    }
  });

  // Add link click listeners to close menu
  const mobileLinks = document.querySelectorAll('.nav-mobile-link, .nav-mobile-cta-button');
  mobileLinks.forEach(link => {
    link.addEventListener('click', handleMobileLinkClick);
  });

  // Close menu when clicking outside content area
  document.addEventListener('click', (event) => {
    if (nav.classList.contains('nav-mobile-expanded') &&
        !nav.contains(event.target)) {
      // Check if click was on any toggle button
      const clickedToggle = Array.from(mobileToggles).some(toggle =>
        toggle.contains(event.target)
      );
      if (!clickedToggle) {
        closeMobileMenu();
      }
    }
  });

  // Handle escape key
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && nav.classList.contains('nav-mobile-expanded')) {
      closeMobileMenu();
    }
  });

  // Handle window resize to close mobile menu on desktop
  window.addEventListener('resize', () => {
    if (window.innerWidth > 1024 && nav.classList.contains('nav-mobile-expanded')) {
      closeMobileMenu();
    }
  });
}